<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="c971.NewCourse"
             Title="{Binding Name}">

    <ScrollView>
        <VerticalStackLayout Padding="20" Spacing="15">

            <Label Text="Course Name" FontAttributes="Bold" />
            <Entry Text="{Binding Name, Mode=TwoWay}" Placeholder="Enter course name" />

            <Label Text="Start Date" FontAttributes="Bold" />
            <DatePicker x:Name="startDatePicker"
            Date="{Binding StartDate, Mode=TwoWay}"
            DateSelected="OnStartDateSelected" />

            <Label Text="End Date" FontAttributes="Bold" />
            <DatePicker x:Name="endDatePicker"
            Date="{Binding EndDate, Mode=TwoWay}"
            DateSelected="OnEndDateSelected" />

            <Label Text="Status" FontAttributes="Bold" />
            <Picker x:Name="statusPicker" Title="Select Status" SelectedItem="{Binding Status, Mode=TwoWay}">
                <Picker.ItemsSource>
                    <x:Array Type="{x:Type x:String}">
                        <x:String>Planned</x:String>
                        <x:String>In Progress</x:String>
                        <x:String>Completed</x:String>
                        <x:String>Dropped</x:String>
                    </x:Array>
                </Picker.ItemsSource>
            </Picker>

            <Label Text="Instructor Name" FontAttributes="Bold" />
            <Entry Text="{Binding InstructorName, Mode=TwoWay}" Placeholder="Full name" />

            <Label Text="Phone" FontAttributes="Bold" />
            <Entry Text="{Binding InstructorPhone, Mode=TwoWay}" Placeholder="Phone number" Keyboard="Telephone" />

            <Label Text="Email" FontAttributes="Bold" />
            <Entry Text="{Binding InstructorEmail, Mode=TwoWay}" Placeholder="Email address" Keyboard="Email" />

            <Label Text="Notes" FontAttributes="Bold" />
            <Editor Text="{Binding Notes, Mode=TwoWay}" Placeholder="Optional notes" AutoSize="TextChanges" />

            <HorizontalStackLayout>
                <Label Text="Enable Notifications?" VerticalOptions="Center" />
                <Switch IsToggled="{Binding Notifications, Mode=TwoWay}" />
            </HorizontalStackLayout>

            <Button Text="Save" Clicked="OnSaveClicked" />

            <Button x:Name="deleteButton"
                    Text="Delete Course"
                    BackgroundColor="Red"
                    TextColor="White"
                    Clicked="OnDeleteClicked" />
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
