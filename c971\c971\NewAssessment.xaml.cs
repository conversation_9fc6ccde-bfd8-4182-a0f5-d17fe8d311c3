﻿// NewAssessmentPage.xaml.cs
namespace c971;

public partial class NewAssessment : ContentPage
{
    private readonly LocalDBService _db;
    private readonly Course _course;
    private readonly Assessment _assessment;

    public NewAssessment(Course course, LocalDBService db, Assessment? assessment = null)
    {
        InitializeComponent();
        _db = db;
        _course = course;
        _assessment = assessment ?? new Assessment
        {
            Name = "New Assessment",
            CourseId = _course.Id,
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddDays(7),
            Notifications = false,
            Notes = ""
        };

        BindingContext = _assessment;
        deleteButton.IsVisible = _assessment.Id != 0;

        Task.Run(async () => await InitializeAsync());
    }

    private async Task InitializeAsync()
    {

        if (_assessment.StartDate < _course.StartDate)
            _assessment.StartDate = _course.StartDate;
        if (_assessment.EndDate > _course.EndDate)
            _assessment.EndDate = _course.EndDate;

        MainThread.BeginInvokeOnMainThread(() =>
        {
            UpdateDateConstraints();
        });
    }

    private async void OnSaveClicked(object sender, EventArgs e)
    {
        if (string.IsNullOrWhiteSpace(_assessment.Name))
        {
            await DisplayAlert("Validation Error", "Name is required.", "OK");
            return;
        }

        if (string.IsNullOrEmpty(_assessment.Type))
        {
            await DisplayAlert("Validation Error", "Type is required.", "OK");
            return;
        }

        // Check if this type already exists for this course (only for new assessments)
        if (_assessment.Id == 0)
        {
            var existingAssessments = await _db.GetAssessments();
            var assessmentsForCourse = existingAssessments.Where(a => a.CourseId == _course.Id);

            if (assessmentsForCourse.Any(a => a.Type == _assessment.Type))
            {
                await DisplayAlert("Validation Error", $"A {_assessment.Type} assessment already exists for this course.", "OK");
                return;
            }
        }

        if (_assessment.Id == 0)
            await _db.CreateAssessment(_assessment);
        else
            await _db.UpdateAssessment(_assessment);

        await Navigation.PopAsync();
    }

    private async void OnDeleteClicked(object sender, EventArgs e)
    {
        bool confirm = await DisplayAlert("Confirm", "Delete this assessment?", "Yes", "No");
        if (confirm)
        {
            await _db.DeleteAssessment(_assessment);
            await Navigation.PopAsync();
        }
    }

    private void UpdateDateConstraints()
    {
        startDatePicker.MinimumDate = _course.StartDate;
        startDatePicker.MaximumDate = _assessment.EndDate <= _course.EndDate ? _assessment.EndDate : _course.EndDate;

        endDatePicker.MinimumDate = _assessment.StartDate >= _course.StartDate ? _assessment.StartDate : _course.StartDate;
        endDatePicker.MaximumDate = _course.EndDate;
    }

    private void OnStartDateSelected(object sender, DateChangedEventArgs e)
    {
        if (e.NewDate < _course.StartDate)
        {
            _assessment.StartDate = _course.StartDate;
        }
        else if (e.NewDate > _assessment.EndDate)
        {
            _assessment.EndDate = e.NewDate > _course.EndDate ? _course.EndDate : e.NewDate;
            _assessment.StartDate = e.NewDate > _course.EndDate ? _course.EndDate : e.NewDate;
        }
        else
        {
            _assessment.StartDate = e.NewDate;
        }

        UpdateDateConstraints();
    }

    private void OnEndDateSelected(object sender, DateChangedEventArgs e)
    {
        if (e.NewDate > _course.EndDate)
        {
            _assessment.EndDate = _course.EndDate;
        }
        else if (e.NewDate < _assessment.StartDate)
        {
            _assessment.StartDate = e.NewDate < _course.StartDate ? _course.StartDate : e.NewDate;
            _assessment.EndDate = e.NewDate < _course.StartDate ? _course.StartDate : e.NewDate;
        }
        else
        {
            _assessment.EndDate = e.NewDate;
        }

        UpdateDateConstraints();
    }
}
