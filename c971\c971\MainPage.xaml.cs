﻿using System.Threading.Tasks;

namespace c971
{
    public partial class MainPage : ContentPage
    {
        private readonly LocalDBService _db;

        LocalDBService localDBService;

        public MainPage(LocalDBService dbService)
        {
            InitializeComponent();
            _db = dbService;
            InitializeData();
        }
        protected override void OnAppearing()
        {
            base.OnAppearing();
            InitializeData();
        }

        private async void InitializeData()
        {
            //foreach (Term term in await _db.GetTerms())
            //{
            //    if (term.Id > 1)
            //    {
            //        await _db.DeleteTerm(term);
            //    }
            //}

            if (_db.GetTermById(1) == null)
            {
                await _db.CreateTerm(new Term
                {
                    Name = "Term 1",
                    StartDate = DateTime.Today,
                    EndDate = DateTime.Today.AddDays(30)
                });
            }

            var terms = await _db.GetTerms();
            terms.Sort((x, y) => DateTime.Compare(x.StartDate, y.StartDate));
            listView.ItemsSource = terms;
        }

        private async void OnEditButtonClicked(object sender, EventArgs e)
        {
            var button = (Button)sender;
            var term = (Term)button.BindingContext;

            await Navigation.PushAsync(new NewTerm(term, _db));
        }

        private async void OnAddNewTermClicked(object sender, EventArgs e)
        {
            Term newTerm = new Term();
            await Navigation.PushAsync(new NewTerm(newTerm, _db));
        }

        private async void OnTermClicked(object sender, ItemTappedEventArgs e)
        {
            var tappedTerm = (Term)e.Item;
            await Navigation.PushAsync(new CourseList(tappedTerm, _db));
        }
    }

}
