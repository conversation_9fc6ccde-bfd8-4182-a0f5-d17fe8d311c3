﻿using System.Threading.Tasks;

namespace c971
{
    public partial class MainPage : ContentPage
    {
        private readonly LocalDBService _db;

        LocalDBService localDBService;

        public MainPage(LocalDBService dbService)
        {
            InitializeComponent();
            _db = dbService;
            InitializeData();
        }
        protected override void OnAppearing()
        {
            base.OnAppearing();
            InitializeData();
        }

        private async void InitializeData()
        {
            await CreateSampleDataAsync();

            var terms = await _db.GetTerms();
            terms.Sort((x, y) => DateTime.Compare(x.StartDate, y.StartDate));
            listView.ItemsSource = terms;
        }

        private async Task CreateSampleDataAsync()
        {
            // Check if sample data already exists
            var existingTerms = await _db.GetTerms();
            if (existingTerms.Any())
                return;

            // Create sample term
            var sampleTerm = new Term
            {
                Name = "Spring 2024",
                StartDate = DateTime.Today,
                EndDate = DateTime.Today.AddDays(120),
                Notifications = true
            };
            await _db.CreateTerm(sampleTerm);

            // Get the created term to get its ID
            var terms = await _db.GetTerms();
            var createdTerm = terms.FirstOrDefault(t => t.Name == "Spring 2024");

            if (createdTerm != null)
            {
                // Create sample course with required instructor information
                var sampleCourse = new Course
                {
                    Name = "Mobile Application Development",
                    StartDate = DateTime.Today.AddDays(7),
                    EndDate = DateTime.Today.AddDays(90),
                    Status = "In Progress",
                    InstructorName = "Anika Patel",
                    InstructorPhone = "************",
                    InstructorEmail = "<EMAIL>",
                    Notes = "This course covers mobile app development using C# and .NET MAUI framework.",
                    Notifications = true,
                    TermId = createdTerm.Id
                };
                await _db.CreateCourse(sampleCourse);

                // Get the created course to get its ID
                var courses = await _db.GetCourses();
                var createdCourse = courses.FirstOrDefault(c => c.Name == "Mobile Application Development");

                if (createdCourse != null)
                {
                    // Create two sample assessments
                    var objectiveAssessment = new Assessment
                    {
                        Name = "Final Exam",
                        Type = "Objective",
                        StartDate = DateTime.Today.AddDays(75),
                        EndDate = DateTime.Today.AddDays(75),
                        Notes = "Comprehensive exam covering all course materials.",
                        Notifications = true,
                        CourseId = createdCourse.Id
                    };
                    await _db.CreateAssessment(objectiveAssessment);

                    var performanceAssessment = new Assessment
                    {
                        Name = "Mobile App Project",
                        Type = "Performance",
                        StartDate = DateTime.Today.AddDays(30),
                        EndDate = DateTime.Today.AddDays(85),
                        Notes = "Develop a complete mobile application demonstrating course concepts.",
                        Notifications = true,
                        CourseId = createdCourse.Id
                    };
                    await _db.CreateAssessment(performanceAssessment);
                }
            }
        }

        private async void OnEditButtonClicked(object sender, EventArgs e)
        {
            var button = (Button)sender;
            var term = (Term)button.BindingContext;

            await Navigation.PushAsync(new NewTerm(term, _db));
        }

        private async void OnAddNewTermClicked(object sender, EventArgs e)
        {
            Term newTerm = new Term();
            await Navigation.PushAsync(new NewTerm(newTerm, _db));
        }

        private async void OnTermClicked(object sender, ItemTappedEventArgs e)
        {
            var tappedTerm = (Term)e.Item;
            await Navigation.PushAsync(new CourseList(tappedTerm, _db));
        }
    }

}
