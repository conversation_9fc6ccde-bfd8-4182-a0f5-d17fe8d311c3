﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="c971.CourseInfo"
             Title="{Binding Name}">

    <Grid RowDefinitions="Auto, *, Auto" Padding="20">
        <ScrollView>
            <VerticalStackLayout Spacing="15">
            <Label Text="Course Details" FontSize="24" FontAttributes="Bold"/>

            <HorizontalStackLayout>
                <Label Text="Status: " FontAttributes="Bold"/>
                <Label Text="{Binding Status}" />
            </HorizontalStackLayout>

            <HorizontalStackLayout>
                <Label Text="Instructor:" FontAttributes="Bold"/>
                <Label Text="{Binding InstructorName}" />
            </HorizontalStackLayout>

            <HorizontalStackLayout>
                <Label Text="Instructor Phone: " FontAttributes="Bold"/>
                <Label Text="{Binding InstructorPhone}" />
            </HorizontalStackLayout>

            <HorizontalStackLayout>
                <Label Text="Instructor Email: " FontAttributes="Bold"/>
                <Label Text="{Binding InstructorEmail}" />
            </HorizontalStackLayout>

            <HorizontalStackLayout>
                <Label Text="Start Date: " FontAttributes="Bold"/>
                <Label Text="{Binding StartDate, StringFormat='{0:MMMM dd, yyyy}'}" />
            </HorizontalStackLayout>

            <HorizontalStackLayout>
                <Label Text="End Date: " FontAttributes="Bold"/>
                <Label Text="{Binding EndDate, StringFormat='{0:MMMM dd, yyyy}'}" />
            </HorizontalStackLayout>

            <Label Text="Notes:" FontAttributes="Bold"/>
            <Label Text="{Binding Notes}" LineBreakMode="WordWrap"/>

            <Button Text="Share Notes" Clicked="OnShareNotesClicked" />

            <Label Text="Assessments:" FontSize="20" FontAttributes="Bold" />



            <ListView x:Name="listView"
                  HasUnevenRows="True"
                  SeparatorVisibility="None"
                  SelectionMode="None"
                  ItemTapped="OnAssessmentClicked">
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <ViewCell>
                            <Border Stroke="LightGray" StrokeThickness="1" Padding="10" Margin="5">
                                <Grid ColumnDefinitions="*, Auto">
                                    <VerticalStackLayout Grid.Column="0" Spacing="4">
                                        <Label Text="{Binding Name}" FontAttributes="Bold" FontSize="16" />
                                        <Label Text="{Binding StartDate, StringFormat='Start: {0:MMM dd, yyyy}'}" FontSize="12" />
                                        <Label Text="{Binding EndDate, StringFormat='End: {0:MMM dd, yyyy}'}" FontSize="12" />
                                    </VerticalStackLayout>
                                        <Button Grid.Column="1"
                                        Text="✏️"
                                        BackgroundColor="Transparent"
                                        FontSize="18"
                                        Clicked="OnEditButtonClicked"/>
                                    </Grid>
                            </Border>
                        </ViewCell>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </ListView>


            <Button Grid.Row="2"
                Text="Add New Assessment"
                Clicked="OnAddAssessmentClicked"
                Margin="0,10,0,0"/>
        </VerticalStackLayout>
        </ScrollView>
    </Grid>
</ContentPage>
