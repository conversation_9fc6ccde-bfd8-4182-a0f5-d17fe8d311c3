﻿using Microsoft.Extensions.Logging;
using Plugin.LocalNotification;
using c971.Services;

namespace c971
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .UseLocalNotification()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                    fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                });
            builder.Services.AddSingleton<LocalDBService>();
            builder.Services.AddSingleton<Services.INotificationService, NotificationService>();
            builder.Services.AddTransient<MainPage>();
            builder.Services.AddTransient<App>();
            builder.ConfigureFonts(fonts => {
                fonts.AddFont("FontAwesome.otf", "FontAwesomeSolid");
            });
#if DEBUG
            builder.Logging.AddDebug();
#endif

            return builder.Build();
        }
    }
}
