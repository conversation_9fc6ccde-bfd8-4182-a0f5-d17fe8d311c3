namespace c971;

public partial class NewTerm : ContentPage
{
    private readonly Term _term;
    private readonly LocalDBService _db;

    public NewTerm(Term term, LocalDBService dbService)
    {
        _term = term ?? new Term
        {
            Name = "New Term",
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddDays(30),
            Notifications = true
        };

        _term.StartDate = _term.StartDate == DateTime.MinValue ? DateTime.Today : _term.StartDate;
        _term.EndDate = _term.EndDate == DateTime.MinValue ? DateTime.Today.AddDays(30) : _term.EndDate;

        BindingContext = _term;
        _db = dbService;
        InitializeComponent();

        deleteButton.IsVisible = _term.Id != 0;
    }

    private async void OnSaveClicked(object sender, EventArgs e)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(_term.Name))
            errors.Add("Term name is required.");

        if (_term.StartDate > _term.EndDate)
            errors.Add("Start date cannot be after end date.");

        if (errors.Any())
        {
            await DisplayAlert("Validation Error", string.Join("\n", errors), "OK");
            return;
        }

        if (_term.Id == 0)
            await _db.CreateTerm(_term);
        else
            await _db.UpdateTerm(_term);

        await Navigation.PopAsync();
    }


    private async void OnDeleteClicked(object sender, EventArgs e)
    {
        bool confirm = await DisplayAlert("Confirm", "Delete this term?", "Yes", "No");
        if (confirm)
        {
            await _db.DeleteTerm(_term);
            await Navigation.PopAsync();
        }
    }

    private void OnStartDateSelected(object sender, DateChangedEventArgs e)
    {
        if (e.NewDate > _term.EndDate)
        {
            _term.EndDate = e.NewDate;
        }
        _term.StartDate = e.NewDate;
        UpdateDateConstraints();
    }

    private void OnEndDateSelected(object sender, DateChangedEventArgs e)
    {
        if (e.NewDate < _term.StartDate)
        {
            _term.StartDate = e.NewDate;
        }
        _term.EndDate = e.NewDate;
        UpdateDateConstraints();
    }

    private void UpdateDateConstraints()
    {
        var startDatePicker = this.FindByName<DatePicker>("startDatePicker");
        var endDatePicker = this.FindByName<DatePicker>("endDatePicker");

        startDatePicker.MinimumDate = _term.StartDate;
        startDatePicker.MaximumDate = endDatePicker.Date;
        endDatePicker.MinimumDate = _term.StartDate;
        endDatePicker.MaximumDate = _term.EndDate;
    }
}