using System;
using System.Threading.Tasks;

namespace c971.Services
{
    public interface INotificationService
    {
        /// <summary>
        /// Schedule a notification for a course start date
        /// </summary>
        /// <param name="course">The course to schedule notification for</param>
        /// <returns>The notification ID</returns>
        Task<int> ScheduleCourseStartNotificationAsync(Course course);

        /// <summary>
        /// Schedule a notification for a course end date
        /// </summary>
        /// <param name="course">The course to schedule notification for</param>
        /// <returns>The notification ID</returns>
        Task<int> ScheduleCourseEndNotificationAsync(Course course);

        /// <summary>
        /// Schedule a notification for an assessment start date
        /// </summary>
        /// <param name="assessment">The assessment to schedule notification for</param>
        /// <returns>The notification ID</returns>
        Task<int> ScheduleAssessmentStartNotificationAsync(Assessment assessment);

        /// <summary>
        /// Schedule a notification for an assessment end date
        /// </summary>
        /// <param name="assessment">The assessment to schedule notification for</param>
        /// <returns>The notification ID</returns>
        Task<int> ScheduleAssessmentEndNotificationAsync(Assessment assessment);

        /// <summary>
        /// Cancel a scheduled notification
        /// </summary>
        /// <param name="notificationId">The ID of the notification to cancel</param>
        Task CancelNotificationAsync(int notificationId);

        /// <summary>
        /// Cancel all notifications for a specific course
        /// </summary>
        /// <param name="courseId">The course ID</param>
        Task CancelCourseNotificationsAsync(int courseId);

        /// <summary>
        /// Cancel all notifications for a specific assessment
        /// </summary>
        /// <param name="assessmentId">The assessment ID</param>
        Task CancelAssessmentNotificationsAsync(int assessmentId);

        /// <summary>
        /// Request notification permissions from the user
        /// </summary>
        Task<bool> RequestPermissionsAsync();
    }
}
