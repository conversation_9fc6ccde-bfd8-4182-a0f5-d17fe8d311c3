namespace c971;

public partial class AssessmentInfo : ContentPage
{
    public AssessmentInfo(Assessment assessment)
    {
        InitializeComponent();
        BindingContext = assessment;
    }

    private async void OnShareNotesClicked(object sender, EventArgs e)
    {
        var assessment = (Assessment)BindingContext;

        if (string.IsNullOrWhiteSpace(assessment.Notes))
        {
            await DisplayAlert("No Notes", "There are no notes to share.", "OK");
            return;
        }

        await Share.RequestAsync(new ShareTextRequest
        {
            Text = assessment.Notes,
            Title = $"Share Notes for {assessment.Name}"
        });
    }
}