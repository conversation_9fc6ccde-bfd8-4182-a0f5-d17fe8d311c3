<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="c971.NewAssessment"
             Title="New Assessment">

    <ScrollView>
        <VerticalStackLayout Padding="20" Spacing="15">
            <Label Text="Assessment Name" FontAttributes="Bold" />
            <Entry Text="{Binding Name, Mode=TwoWay}" Placeholder="Enter assessment name" />
            <Picker x:Name="typePicker" Title="Select Type" SelectedItem="{Binding Type, Mode=TwoWay}">
                <Picker.ItemsSource>
                    <x:Array Type="{x:Type x:String}">
                        <x:String>Objective</x:String>
                        <x:String>Performance</x:String>
                    </x:Array>
                </Picker.ItemsSource>
            </Picker>
            <Label Text="Start Date" FontAttributes="Bold" />
            <DatePicker x:Name="startDatePicker"
                        Date="{Binding StartDate, Mode=TwoWay}"
                        DateSelected="OnStartDateSelected" />

            <Label Text="End Date" FontAttributes="Bold" />
            <DatePicker x:Name="endDatePicker"
                        Date="{Binding EndDate, Mode=TwoWay}"
                        DateSelected="OnEndDateSelected" />

            <Label Text="Notes" FontAttributes="Bold" />
            <Editor Text="{Binding Notes, Mode=TwoWay}" Placeholder="Enter notes (optional)" HeightRequest="100" />

            <HorizontalStackLayout>
                <Label Text="Enable Notifications?" VerticalOptions="Center" />
                <Switch IsToggled="{Binding Notifications, Mode=TwoWay}" />
            </HorizontalStackLayout>

            <Button Text="Save" Clicked="OnSaveClicked" />
            <Button x:Name="deleteButton" Text="Delete Assessment" BackgroundColor="Red" Clicked="OnDeleteClicked" />
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>