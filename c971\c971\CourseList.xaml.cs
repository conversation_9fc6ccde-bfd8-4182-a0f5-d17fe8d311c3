using System.Diagnostics;

namespace c971;

public partial class CourseList : ContentPage
{
    private readonly LocalDBService _db;
    private readonly Term Term;

    public CourseList(Term term, LocalDBService dbService)
    {
        InitializeComponent(); 
        _db = dbService;
        Term = term;
        InitializeData();
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();
        InitializeData();
    }

    private async void InitializeData()
    {
        Debug.WriteLine($"[Init] Term ID: {Term.Id}");

        var coursesForTerm = (await _db.GetCourses())
            .Where(c => c.TermId == Term.Id)
            .ToList();

        coursesForTerm.Sort((x, y) => DateTime.Compare(x.StartDate, y.StartDate));
        listView.ItemsSource = coursesForTerm;
    }

    private async void OnEditButtonClicked(object sender, EventArgs e)
    {
        var button = (Button)sender;
        var Course = (Course)button.BindingContext;

        await Navigation.PushAsync(new NewCourse(Course, Term.Id, _db));
    }

    private async void OnAddNewCourseClicked(object sender, EventArgs e)
    {
        Course newCourse = new Course();
        await Navigation.PushAsync(new NewCourse(newCourse, Term.Id, _db));
    }

    private async void OnCourseClicked(object sender, ItemTappedEventArgs e)
    {
        var tappedCourse = (Course)e.Item;
        await Navigation.PushAsync(new CourseInfo(tappedCourse, _db));
    }
}
