using SQLite;

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ColumnAttribute = SQLite.ColumnAttribute;
using TableAttribute = SQLite.TableAttribute;

namespace c971
{
    [Table("assessment")]
    public class Assessment
    {
        [PrimaryKey]
        [AutoIncrement]
        [Column("id")]
        public int Id { get; set; }

        [ForeignKey("Course")]
        [NotNull]
        [Column("course_id")]
        public int CourseId { get; set; }

        [NotNull]
        [Column("name")]
        public string Name { get; set; }

        [NotNull]
        [Column("type")]
        public string Type { get; set; }

        [NotNull]
        [Column("start_date")]
        public DateTime StartDate { get; set; }

        [NotNull]
        [Column("end_date")]
        public DateTime EndDate { get; set; }

        public string Notes { get; set; }

        [NotNull]
        [Column("notifications")]
        public bool Notifications { get; set; }

        [Column("start_notification_id")]
        public int StartNotificationId { get; set; } = -1;

        [Column("end_notification_id")]
        public int EndNotificationId { get; set; } = -1;
    }
}
