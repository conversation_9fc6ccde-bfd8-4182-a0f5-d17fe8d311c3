﻿using SQLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace c971
{
    public class LocalDBService
    {
        private const string DB_NAME = "application_db.db3";
        private readonly SQLiteAsyncConnection _connection;

        public LocalDBService() {
            _connection = new SQLiteAsyncConnection(Path.Combine(FileSystem.AppDataDirectory, DB_NAME));
            _connection.CreateTableAsync<Term>();
            _connection.CreateTableAsync<Course>();
            _connection.CreateTableAsync<Assessment>();
        }

        // TERMS

        public async Task<List<Term>> GetTerms()
        {
            return await _connection.Table<Term>().ToListAsync();
        }

        public async Task<Term> GetTermById(int id)
        {
            return await _connection.Table<Term>().Where(t => t.Id == id).FirstOrDefaultAsync();
        }

        public async Task CreateTerm(Term term)
        {
            await _connection.InsertAsync(term);
        }

        public async Task UpdateTerm(Term term)
        {
            await _connection.UpdateAsync(term);
        }

        public async Task DeleteTerm(Term term)
        {
            await _connection.DeleteAsync(term);
        }

        //  COURSES

        public async Task<List<Course>> GetCourses()
        {
            return await _connection.Table<Course>().ToListAsync();
        }

        public async Task<Course> GetCourseById(int id)
        {
            return await _connection.Table<Course>().Where(t => t.Id == id).FirstOrDefaultAsync();
        }

        public async Task CreateCourse(Course course)
        {
            await _connection.InsertAsync(course);
        }

        public async Task UpdateCourse(Course course)
        {
            await _connection.UpdateAsync(course);
        }

        public async Task DeleteCourse(Course course)
        {
            await _connection.DeleteAsync(course);
        }

        // ASSESSMENTS
        public async Task<List<Assessment>> GetAssessments()
        {
            return await _connection.Table<Assessment>().ToListAsync();
        }

        public async Task<Assessment> GetAssessmentById(int id)
        {
            return await _connection.Table<Assessment>().Where(t => t.Id == id).FirstOrDefaultAsync();
        }

        public async Task CreateAssessment(Assessment assessment)
        {
            await _connection.InsertAsync(assessment);
        }

        public async Task UpdateAssessment(Assessment assessment)
        {
            await _connection.UpdateAsync(assessment);
        }

        public async Task DeleteAssessment(Assessment assessment)
        {
            await _connection.DeleteAsync(assessment);
        }
    }
}
