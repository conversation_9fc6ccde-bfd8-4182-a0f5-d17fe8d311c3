﻿using SQLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using c971.Services;

namespace c971
{
    public class LocalDBService
    {
        private const string DB_NAME = "application_db.db3";
        private readonly SQLiteAsyncConnection _connection;
        private readonly INotificationService _notificationService;

        public LocalDBService(INotificationService notificationService) {
            _notificationService = notificationService;
            _connection = new SQLiteAsyncConnection(Path.Combine(FileSystem.AppDataDirectory, DB_NAME));
            _connection.CreateTableAsync<Term>();
            _connection.CreateTableAsync<Course>();
            _connection.CreateTableAsync<Assessment>();
        }

        // TERMS

        public async Task<List<Term>> GetTerms()
        {
            return await _connection.Table<Term>().ToListAsync();
        }

        public async Task<Term> GetTermById(int id)
        {
            return await _connection.Table<Term>().Where(t => t.Id == id).FirstOrDefaultAsync();
        }

        public async Task CreateTerm(Term term)
        {
            await _connection.InsertAsync(term);
        }

        public async Task UpdateTerm(Term term)
        {
            await _connection.UpdateAsync(term);
        }

        public async Task DeleteTerm(Term term)
        {
            await _connection.DeleteAsync(term);
        }

        //  COURSES

        public async Task<List<Course>> GetCourses()
        {
            return await _connection.Table<Course>().ToListAsync();
        }

        public async Task<Course> GetCourseById(int id)
        {
            return await _connection.Table<Course>().Where(t => t.Id == id).FirstOrDefaultAsync();
        }

        public async Task CreateCourse(Course course)
        {
            await _connection.InsertAsync(course);
            await ScheduleCourseNotificationsAsync(course);
        }

        public async Task UpdateCourse(Course course)
        {
            // Cancel existing notifications
            await _notificationService.CancelCourseNotificationsAsync(course.Id);

            // Reset notification IDs
            course.StartNotificationId = -1;
            course.EndNotificationId = -1;

            await _connection.UpdateAsync(course);
            await ScheduleCourseNotificationsAsync(course);
        }

        public async Task DeleteCourse(Course course)
        {
            // Cancel notifications before deleting
            await _notificationService.CancelCourseNotificationsAsync(course.Id);
            await _connection.DeleteAsync(course);
        }

        // ASSESSMENTS
        public async Task<List<Assessment>> GetAssessments()
        {
            return await _connection.Table<Assessment>().ToListAsync();
        }

        public async Task<Assessment> GetAssessmentById(int id)
        {
            return await _connection.Table<Assessment>().Where(t => t.Id == id).FirstOrDefaultAsync();
        }

        public async Task CreateAssessment(Assessment assessment)
        {
            await _connection.InsertAsync(assessment);
            await ScheduleAssessmentNotificationsAsync(assessment);
        }

        public async Task UpdateAssessment(Assessment assessment)
        {
            // Cancel existing notifications
            await _notificationService.CancelAssessmentNotificationsAsync(assessment.Id);

            // Reset notification IDs
            assessment.StartNotificationId = -1;
            assessment.EndNotificationId = -1;

            await _connection.UpdateAsync(assessment);
            await ScheduleAssessmentNotificationsAsync(assessment);
        }

        public async Task DeleteAssessment(Assessment assessment)
        {
            // Cancel notifications before deleting
            await _notificationService.CancelAssessmentNotificationsAsync(assessment.Id);
            await _connection.DeleteAsync(assessment);
        }

        // Private helper methods for scheduling notifications
        private async Task ScheduleCourseNotificationsAsync(Course course)
        {
            if (course.Notifications)
            {
                // Schedule start notification
                var startNotificationId = await _notificationService.ScheduleCourseStartNotificationAsync(course);
                if (startNotificationId > 0)
                {
                    course.StartNotificationId = startNotificationId;
                }

                // Schedule end notification
                var endNotificationId = await _notificationService.ScheduleCourseEndNotificationAsync(course);
                if (endNotificationId > 0)
                {
                    course.EndNotificationId = endNotificationId;
                }

                // Update the course with notification IDs if any were scheduled
                if (course.StartNotificationId > 0 || course.EndNotificationId > 0)
                {
                    await _connection.UpdateAsync(course);
                }
            }
        }

        private async Task ScheduleAssessmentNotificationsAsync(Assessment assessment)
        {
            if (assessment.Notifications)
            {
                // Schedule start notification
                var startNotificationId = await _notificationService.ScheduleAssessmentStartNotificationAsync(assessment);
                if (startNotificationId > 0)
                {
                    assessment.StartNotificationId = startNotificationId;
                }

                // Schedule end notification
                var endNotificationId = await _notificationService.ScheduleAssessmentEndNotificationAsync(assessment);
                if (endNotificationId > 0)
                {
                    assessment.EndNotificationId = endNotificationId;
                }

                // Update the assessment with notification IDs if any were scheduled
                if (assessment.StartNotificationId > 0 || assessment.EndNotificationId > 0)
                {
                    await _connection.UpdateAsync(assessment);
                }
            }
        }
    }
}
