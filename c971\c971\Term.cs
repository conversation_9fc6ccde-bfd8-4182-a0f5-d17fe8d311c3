﻿using SQLite;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ColumnAttribute = SQLite.ColumnAttribute;
using TableAttribute = SQLite.TableAttribute;

namespace c971
{
    [Table("term")]
    public class Term
    {
        [PrimaryKey]
        [AutoIncrement]
        [Column("id")]
        public int Id {  get; set; }

        [NotNull]
        [Column("name")]
        public string Name { get; set; }

        [NotNull]
        [Column("start_date")]
        public DateTime StartDate { get; set; }

        [NotNull]
        [Column("end_date")]
        public DateTime EndDate { get; set; }

        [NotNull]
        [Column("notifications")]
        public bool Notifications { get; set; }

    }
}
