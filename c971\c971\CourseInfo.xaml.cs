using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Maui.Controls;

namespace c971
{
    public partial class CourseInfo : ContentPage
    {
        private readonly Course _course;
        private readonly LocalDBService _db;

        public CourseInfo(Course course, LocalDBService dbService)
        {
            InitializeComponent();
            _course = course;
            _db = dbService;
            BindingContext = _course;
            InitializeData();
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            InitializeData();
        }

        private async void InitializeData()
        {
            var assessmentsForCourse = (await _db.GetAssessments())
            .Where(a => a.CourseId == _course.Id)
            .ToList();

            assessmentsForCourse.Sort((x, y) => DateTime.Compare(x.StartDate, y.StartDate));
            listView.ItemsSource = assessmentsForCourse;
        }

        private async void OnShareNotesClicked(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(_course.Notes))
            {
                await Share.Default.RequestAsync(new ShareTextRequest
                {
                    Text = _course.Notes,
                    Title = "Course Notes"
                });
            }
            else
            {
                await DisplayAlert("No Notes", "There are no notes to share.", "OK");
            }
        }

        private async void OnAssessmentClicked(object sender, ItemTappedEventArgs e)
        {
            var selectedAssessment = (Assessment)e.Item;
            await Navigation.PushAsync(new AssessmentInfo(selectedAssessment));

        }

        private async void OnEditButtonClicked(object sender, EventArgs e)
        {
            if (sender is Button button && button.BindingContext is Assessment assessment)
            {
                await Navigation.PushAsync(new NewAssessment(_course, _db, assessment));
            }
            else
            {
                await DisplayAlert("Error", "Assessment could not be loaded.", "OK");
            }
        }

        private async void OnAddAssessmentClicked(object sender, EventArgs e)
        {
            await Navigation.PushAsync(new NewAssessment(_course, _db));
        }

        private async void OnEditCourseClicked(object sender, EventArgs e)
        {
            await Navigation.PushAsync(new NewCourse(_course, _course.TermId, _db));
        }
    }
}