﻿using c971.Services;

namespace c971
{
    public partial class App : Application
    {
        private readonly INotificationService _notificationService;

        public App(MainPage mainPage, INotificationService notificationService)
        {
            InitializeComponent();
            _notificationService = notificationService;

            MainPage = new NavigationPage(mainPage);
        }

        protected override async void OnStart()
        {
            base.OnStart();

            // Request notification permissions when the app starts
            await _notificationService.RequestPermissionsAsync();
        }
    }
}