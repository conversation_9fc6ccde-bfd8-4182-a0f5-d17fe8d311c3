<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="c971.NewTerm"
             Title="{Binding Name}">

    <ScrollView>
        <VerticalStackLayout Padding="20" Spacing="15">
            <Label Text="Name" FontAttributes="Bold" />
            <Entry Text="{Binding Name, Mode=TwoWay}"
                   Placeholder="Enter term name" />

            <Label Text="Start Date" FontAttributes="Bold" />
            <DatePicker x:Name="startDatePicker"
                        Date="{Binding StartDate, Mode=TwoWay}"
                        DateSelected="OnStartDateSelected"
                        MinimumDate="{Binding StartDate}"
                        MaximumDate="{Binding EndDate}" />

            <Label Text="End Date" FontAttributes="Bold" />
            <DatePicker x:Name="endDatePicker"
                        Date="{Binding EndDate, Mode=TwoWay}"
                        DateSelected="OnEndDateSelected"
                        MinimumDate="{Binding StartDate}" />
            
            <HorizontalStackLayout>
                <Label Text="Enable Notifications?" VerticalOptions="Center" />
                <Switch IsToggled="{Binding Notifications, Mode=TwoWay}" />
            </HorizontalStackLayout>

            <Button Text="Save"
                    Clicked="OnSaveClicked" />
            
            <Button x:Name="deleteButton"
                    Text="Delete term"
                    BackgroundColor="Red"
                    Clicked="OnDeleteClicked"/>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
