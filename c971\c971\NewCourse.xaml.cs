using System.Text.RegularExpressions;

namespace c971;

public partial class NewCourse : ContentPage
{
    private readonly Course _course;
    private readonly LocalDBService _db;
    private readonly int _termId;
    private Term _term; // Changed from readonly to non-readonly

    public NewCourse(Course course, int termId, LocalDBService dbService)
    {
        _termId = termId;
        _db = dbService;
        _course = course ?? new Course
        {
            Name = "New Course",
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddDays(30),
            Status = "Planned",
            InstructorName = "",
            InstructorPhone = "",
            InstructorEmail = "",
            Notifications = false,
            TermId = _termId
        };

        InitializeComponent();
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();

        _term = await _db.GetTermById(_termId);

        if (_term != null)
        {
            if (_course.StartDate < _term.StartDate)
                _course.StartDate = _term.StartDate;

            if (_course.EndDate > _term.EndDate)
                _course.EndDate = _term.EndDate;
        }

        BindingContext = _course;

        UpdateDateConstraints();

        deleteButton.IsVisible = _course.Id != 0;
    }

    private async void OnSaveClicked(object sender, EventArgs e)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(_course.Name))
            errors.Add("Course name is required.");

        if (string.IsNullOrWhiteSpace(_course.Status))
            errors.Add("Course status is required.");

        if (string.IsNullOrWhiteSpace(_course.InstructorName))
            errors.Add("Instructor name is required.");

        if (string.IsNullOrWhiteSpace(_course.InstructorPhone))
            errors.Add("Instructor phone is required.");

        if (string.IsNullOrWhiteSpace(_course.InstructorEmail))
            errors.Add("Instructor email is required.");
        else if (!Regex.IsMatch(_course.InstructorEmail, @"^[^@\s]+@[^@\s]+\.[^@\s]+$"))
            errors.Add("Instructor email format is invalid.");

        // Check course limit (6 courses per term) - only for new courses
        if (_course.Id == 0)
        {
            var existingCourses = await _db.GetCourses();
            var coursesInTerm = existingCourses.Where(c => c.TermId == _termId).Count();
            if (coursesInTerm >= 6)
                errors.Add("Maximum of 6 courses allowed per term.");
        }

        // Date validation
        if (_course.StartDate < _term.StartDate || _course.EndDate > _term.EndDate)
            errors.Add("Course dates must be within term start and end dates.");

        if (_course.StartDate > _course.EndDate)
            errors.Add("Start date cannot be after end date.");

        if (errors.Any())
        {
            await DisplayAlert("Validation Errors", string.Join("\n", errors), "OK");
            return;
        }

        _course.TermId = _termId;

        if (_course.Id == 0)
            await _db.CreateCourse(_course);
        else
            await _db.UpdateCourse(_course);

        await Navigation.PopAsync();
    }

    private async void OnDeleteClicked(object sender, EventArgs e)
    {
        bool confirm = await DisplayAlert("Confirm", "Delete this course?", "Yes", "No");
        if (confirm)
        {
            await _db.DeleteCourse(_course);
            await Navigation.PopAsync();
        }
    }

    private void UpdateDateConstraints()
    {
        startDatePicker.MinimumDate = _term.StartDate;
        startDatePicker.MaximumDate = _course.EndDate <= _term.EndDate ? _course.EndDate : _term.EndDate;

        endDatePicker.MinimumDate = _course.StartDate >= _term.StartDate ? _course.StartDate : _term.StartDate;
        endDatePicker.MaximumDate = _term.EndDate;
    }

    private void OnStartDateSelected(object sender, DateChangedEventArgs e)
    {
        if (e.NewDate < _term.StartDate)
        {
            _course.StartDate = _term.StartDate;
        }
        else if (e.NewDate > _course.EndDate)
        {
            _course.EndDate = e.NewDate > _term.EndDate ? _term.EndDate : e.NewDate;
            _course.StartDate = e.NewDate > _term.EndDate ? _term.EndDate : e.NewDate;
        }
        else
        {
            _course.StartDate = e.NewDate;
        }

        UpdateDateConstraints();
    }

    private void OnEndDateSelected(object sender, DateChangedEventArgs e)
    {
        if (e.NewDate > _term.EndDate)
        {
            _course.EndDate = _term.EndDate;
        }
        else if (e.NewDate < _course.StartDate)
        {
            _course.StartDate = e.NewDate < _term.StartDate ? _term.StartDate : e.NewDate;
            _course.EndDate = e.NewDate < _term.StartDate ? _term.StartDate : e.NewDate;
        }
        else
        {
            _course.EndDate = e.NewDate;
        }

        UpdateDateConstraints();
    }
}
