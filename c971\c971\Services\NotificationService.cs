using Plugin.LocalNotification;
using System;
using System.Threading.Tasks;

namespace c971.Services
{
    public class NotificationService : INotificationService
    {
        private int _nextNotificationId = 1000; // Start from 1000 to avoid conflicts
        private bool _permissionsRequested = false;

        public async Task<bool> RequestPermissionsAsync()
        {
            if (!_permissionsRequested)
            {
                _permissionsRequested = true;
                var result = await LocalNotificationCenter.Current.RequestNotificationPermission();
                return result;
            }
            return true;
        }

        private async Task<bool> EnsurePermissionsAsync()
        {
            return await RequestPermissionsAsync();
        }

        public async Task<int> ScheduleCourseStartNotificationAsync(Course course)
        {
            if (!course.Notifications || course.StartDate <= DateTime.Now)
                return -1;

            if (!await EnsurePermissionsAsync())
                return -1;

            var notificationId = _nextNotificationId++;

            var notification = new NotificationRequest
            {
                NotificationId = notificationId,
                Title = "Course Starting Today",
                Subtitle = course.Name,
                Description = $"Your course '{course.Name}' is starting today!",
                BadgeNumber = 1,
                Schedule = new NotificationRequestSchedule
                {
                    NotifyTime = course.StartDate.Date.AddHours(8) // 8 AM on start date
                }
            };

            await LocalNotificationCenter.Current.Show(notification);
            return notificationId;
        }

        public async Task<int> ScheduleCourseEndNotificationAsync(Course course)
        {
            if (!course.Notifications || course.EndDate <= DateTime.Now)
                return -1;

            if (!await EnsurePermissionsAsync())
                return -1;

            var notificationId = _nextNotificationId++;

            var notification = new NotificationRequest
            {
                NotificationId = notificationId,
                Title = "Course Ending Today",
                Subtitle = course.Name,
                Description = $"Your course '{course.Name}' is ending today!",
                BadgeNumber = 1,
                Schedule = new NotificationRequestSchedule
                {
                    NotifyTime = course.EndDate.Date.AddHours(8) // 8 AM on end date
                }
            };

            await LocalNotificationCenter.Current.Show(notification);
            return notificationId;
        }

        public async Task<int> ScheduleAssessmentStartNotificationAsync(Assessment assessment)
        {
            if (!assessment.Notifications || assessment.StartDate <= DateTime.Now)
                return -1;

            if (!await EnsurePermissionsAsync())
                return -1;

            var notificationId = _nextNotificationId++;

            var notification = new NotificationRequest
            {
                NotificationId = notificationId,
                Title = "Assessment Starting Today",
                Subtitle = assessment.Name,
                Description = $"Your {assessment.Type.ToLower()} '{assessment.Name}' is starting today!",
                BadgeNumber = 1,
                Schedule = new NotificationRequestSchedule
                {
                    NotifyTime = assessment.StartDate.Date.AddHours(8) // 8 AM on start date
                }
            };

            await LocalNotificationCenter.Current.Show(notification);
            return notificationId;
        }

        public async Task<int> ScheduleAssessmentEndNotificationAsync(Assessment assessment)
        {
            if (!assessment.Notifications || assessment.EndDate <= DateTime.Now)
                return -1;

            if (!await EnsurePermissionsAsync())
                return -1;

            var notificationId = _nextNotificationId++;

            var notification = new NotificationRequest
            {
                NotificationId = notificationId,
                Title = "Assessment Due Today",
                Subtitle = assessment.Name,
                Description = $"Your {assessment.Type.ToLower()} '{assessment.Name}' is due today!",
                BadgeNumber = 1,
                Schedule = new NotificationRequestSchedule
                {
                    NotifyTime = assessment.EndDate.Date.AddHours(8) // 8 AM on due date
                }
            };

            await LocalNotificationCenter.Current.Show(notification);
            return notificationId;
        }

        public Task CancelNotificationAsync(int notificationId)
        {
            if (notificationId > 0)
            {
                LocalNotificationCenter.Current.Cancel(notificationId);
            }
            return Task.CompletedTask;
        }

        public async Task CancelCourseNotificationsAsync(int courseId)
        {
            // Note: This method would need access to the database to get the course
            // and cancel its notifications. For now, individual notification cancellation
            // should be handled by the calling code that has access to the course object.
            await Task.CompletedTask;
        }

        public async Task CancelAssessmentNotificationsAsync(int assessmentId)
        {
            // Note: This method would need access to the database to get the assessment
            // and cancel its notifications. For now, individual notification cancellation
            // should be handled by the calling code that has access to the assessment object.
            await Task.CompletedTask;
        }

        public async Task CancelCourseNotificationsAsync(Course course)
        {
            if (course.StartNotificationId > 0)
            {
                await CancelNotificationAsync(course.StartNotificationId);
            }
            if (course.EndNotificationId > 0)
            {
                await CancelNotificationAsync(course.EndNotificationId);
            }
        }

        public async Task CancelAssessmentNotificationsAsync(Assessment assessment)
        {
            if (assessment.StartNotificationId > 0)
            {
                await CancelNotificationAsync(assessment.StartNotificationId);
            }
            if (assessment.EndNotificationId > 0)
            {
                await CancelNotificationAsync(assessment.EndNotificationId);
            }
        }
    }
}
