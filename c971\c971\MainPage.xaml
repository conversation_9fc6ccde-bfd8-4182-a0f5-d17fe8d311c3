﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"  
          xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"  
          x:Class="c971.MainPage">

    <ContentPage.Content>
        <StackLayout Padding="10">
            <Label Text="Terms"  
                FontSize="24"  
                FontAttributes="Bold"  
                HorizontalOptions="Center" />

            <ListView x:Name="listView"
              ItemsSource="{Binding Terms}"
              HasUnevenRows="True"
              SeparatorVisibility="Default"
              SelectionMode="None"
              ItemTapped="OnTermClicked">  

                <ListView.ItemTemplate>
                    <DataTemplate>
                        <ViewCell>
                            <Border
                              Stroke="LightGray" StrokeThickness="1"
                              Padding="10" Margin="5">
                                <Grid ColumnDefinitions="*, auto"
                                        Padding="10">
                                    <VerticalStackLayout Grid.Column="0" Spacing="2">
                                        <Label Text="{Binding Name}"
                               FontSize="18"
                               FontAttributes="Bold" />
                                        <Label Text="{Binding StartDate, StringFormat='Start: {0:MM/dd/yyyy}'}"
                               FontSize="14"
                               TextColor="Gray" />
                                        <Label Text="{Binding EndDate, StringFormat='End: {0:MM/dd/yyyy}'}"
                               FontSize="14"
                               TextColor="Gray" />
                                    </VerticalStackLayout>

                                    <Button Grid.Column="1"
                                    Text="✏️"
                                    BackgroundColor="Transparent"
                                    FontSize="18"
                                    Clicked="OnEditButtonClicked"/>

                                </Grid>
                            </Border>
                        </ViewCell>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </ListView>
            <Button Text="Add New Term"
                Clicked="OnAddNewTermClicked"
                HorizontalOptions="Center" />
        </StackLayout>
    </ContentPage.Content>
</ContentPage>
