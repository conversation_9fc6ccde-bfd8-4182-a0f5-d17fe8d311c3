﻿using SQLite;

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ColumnAttribute = SQLite.ColumnAttribute;
using TableAttribute = SQLite.TableAttribute;

namespace c971
{
    [Table("course")]
    public class Course
    {
        [PrimaryKey]
        [AutoIncrement]
        [Column("id")]
        public int Id { get; set; }

        [ForeignKey("Term")]
        [NotNull]
        [Column("term_id")]
        public int TermId { get; set; }

        [NotNull]
        [Column("name")]
        public string Name { get; set; }

        [NotNull]
        [Column("start_date")]
        public DateTime StartDate { get; set; }

        [NotNull]
        [Column("end_date")]
        public DateTime EndDate { get; set; }

        [NotNull]
        [Column("status")]
        public string Status { get; set; }

        [NotNull]
        [Column("instructor_name")]
        public string InstructorName { get; set; }

        [NotNull]
        [Column("instructor_phone")]
        public string InstructorPhone { get; set; }

        [NotNull]
        [Column("instructor_email")]
        public string InstructorEmail { get; set; }

        [Column("notes")]
        public string Notes { get; set; }

        [NotNull]
        [Column("notifications")]
        public bool Notifications { get; set; }
    }
}
